PORT=3000

#Secret JWT
JWT_SECRET=admin123
PASSWORD=admin123

# PostGres Local
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=abstraxn

# RabbitMq
RMQ_URL=amqp://admin:admin123@localhost:5672/

# ChatAI SDK URL (for vector processing)
CHATAI_SDK_URL=http://localhost:3001

# Internal API Configuration (for service communication)
# Used for ChatAI-SDK-Clean to update document status
INTERNAL_API_KEY=chatai-internal-2024

# File Upload Configuration
MAX_FILE_SIZE=20971520



#Mail
PROJECT_NAME=abstraxn
SMTP_HOST=smtp.sendgrid.net
SMTP_POST=25
SMTP_USER=apikey
SMTP_PASS=*********************************************************************
SMTP_FROM=<EMAIL>
FRONTEND_URL=http://localhost:5173

#Gateway
GATEWAY_URL=http://*************:8002

#LANDING PAGE INFO
LANDING_URL=https://abstraxn.com

#BlockExplorer API keys
ETHERSCAN=**********************************
POLYGONSCAN=5BWSEJDJ9RIXMKD7SS36TKHCJ653U4PT4N
ZKEVMPOLYGONSCAN=ZHDVW6VVGSMPQS28R4QS5K2Q33BR4ADKH9
BSCSCAN=**********************************

#origin
PAYMASTER_ORIGIN=https://paymaster.abstraxn.com
BUNDLER_ORIGIN=https://bundler.abstraxn.com
RELAYER_ORIGIN=https://relayer.abstraxn.com
CHATAI_ORIGIN=http://localhost:3001

#Socket Service
API_URL=http://localhost:3000
STAGE_API_URL=http://************:3000

#ChatAI Service API Keys
LLAMA_CLOUD_API_KEY=llx-0JczdaEIGFyOG4H4PpapuhA4iZrCWn9KPvNDovijga3m6ZbW
OPENROUTER_API_KEY=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
