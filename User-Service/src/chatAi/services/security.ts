import { Injectable, Logger } from '@nestjs/common';

// ============ SECURE LOGGING SYSTEM ============

enum LogLevel {
  ERROR = 'ERROR',
  WARN = 'WARN',
  INFO = 'INFO',
  DEBUG = 'DEBUG',
  SECURITY = 'SECURITY',
}

interface LogContext {
  userId?: string;
  ip?: string;
  endpoint?: string;
  method?: string;
  userAgent?: string;
}

@Injectable()
export class SecureLogger {
  private readonly logger = new Logger(SecureLogger.name);
  private isProduction = process.env.NODE_ENV === 'production';
  private isDevelopment = process.env.NODE_ENV === 'development';

  private sanitizeData(data: any): any {
    if (typeof data === 'string') {
      // Remove potential sensitive data patterns
      return data
        .replace(/password=[\w\-\.@]+/gi, 'password=***')
        .replace(/token=[\w\-\.]+/gi, 'token=***')
        .replace(/key=[\w\-\.]+/gi, 'key=***')
        .replace(/secret=[\w\-\.]+/gi, 'secret=***')
        .replace(/Bearer\s+[\w\-\.]+/gi, 'Bearer ***')
        .replace(/sk-[\w\-\.]+/gi, 'sk-***')
        .replace(/llx-[\w\-\.]+/gi, 'llx-***');
    }

    if (typeof data === 'object' && data !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(data)) {
        // Skip sensitive fields
        if (
          ['password', 'token', 'key', 'secret', 'authorization'].includes(
            key.toLowerCase(),
          )
        ) {
          sanitized[key] = '***';
        } else {
          sanitized[key] = this.sanitizeData(value);
        }
      }
      return sanitized;
    }

    return data;
  }

  private formatMessage(
    level: LogLevel,
    message: string,
    context?: LogContext,
    data?: any,
  ): string {
    const timestamp = new Date().toISOString();
    const sanitizedData = data ? this.sanitizeData(data) : '';
    const contextStr = context
      ? `[${context.method || ''}${context.endpoint || ''}${context.userId ? ` user:${context.userId}` : ''}${context.ip ? ` ip:${context.ip}` : ''}]`
      : '';

    return `${timestamp} [${level}]${contextStr} ${message}${sanitizedData ? ` ${JSON.stringify(sanitizedData)}` : ''}`;
  }

  security(message: string, context?: LogContext, data?: any) {
    this.logger.log(
      this.formatMessage(LogLevel.SECURITY, message, context, data),
    );
  }

  error(message: string, context?: LogContext, data?: any) {
    // Always log errors, but sanitize sensitive data
    this.logger.error(
      this.formatMessage(
        LogLevel.ERROR,
        message,
        context,
        this.isDevelopment ? data : undefined,
      ),
    );
  }

  warn(message: string, context?: LogContext, data?: any) {
    this.logger.warn(
      this.formatMessage(
        LogLevel.WARN,
        message,
        context,
        this.isDevelopment ? data : undefined,
      ),
    );
  }

  info(message: string, context?: LogContext, data?: any) {
    if (!this.isProduction) {
      this.logger.log(
        this.formatMessage(LogLevel.INFO, message, context, data),
      );
    }
  }

  debug(message: string, context?: LogContext, data?: any) {
    if (this.isDevelopment) {
      this.logger.debug(
        this.formatMessage(LogLevel.DEBUG, message, context, data),
      );
    }
  }

  // Safe error response for clients
  sanitizeErrorForClient(error: any): string {
    if (this.isDevelopment) {
      // In development, show more details for debugging
      return error instanceof Error ? error.message : String(error);
    }

    // In production, return generic messages to prevent information disclosure
    if (error instanceof Error) {
      const message = error.message.toLowerCase();

      // Safe error patterns that can be shown to users
      if (
        message.includes('validation') ||
        message.includes('invalid') ||
        message.includes('not found') ||
        message.includes('access denied') ||
        message.includes('unauthorized') ||
        message.includes('rate limit') ||
        message.includes('file too large') ||
        message.includes('unsupported file')
      ) {
        return error.message;
      }
    }

    // Generic error for everything else
    return 'An internal error occurred. Please try again later.';
  }
}

// ============ RATE LIMITING SYSTEM ============

interface RateLimitEntry {
  timestamp: number;
  count: number;
  size?: number;
  cost?: number;
  tokens?: number;
}

@Injectable()
export class RateLimitingService {
  private readonly logger = new Logger(RateLimitingService.name);

  // Per-user upload tracking
  private userUploadTracking = new Map<
    string,
    Array<{ timestamp: number; size: number }>
  >();

  // AI usage tracking
  private aiUsageTracking = new Map<
    string,
    Array<{ timestamp: number; cost: number; tokens: number }>
  >();

  /**
   * Track user upload and check limits
   */
  trackUserUpload(userId: string, fileSize: number): boolean {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    if (!this.userUploadTracking.has(userId)) {
      this.userUploadTracking.set(userId, []);
    }

    const userUploads = this.userUploadTracking.get(userId)!;

    // Clean old entries (older than 1 hour)
    const recentUploads = userUploads.filter(
      (upload) => now - upload.timestamp < oneHour,
    );

    // Check limits
    const uploadCount = recentUploads.length;
    const totalSize = recentUploads.reduce(
      (sum, upload) => sum + upload.size,
      0,
    );

    // Limits per user per hour
    const MAX_UPLOADS_PER_HOUR = 5;
    const maxFileSizeMB = parseInt(process.env.MAX_FILE_SIZE) || 20; // Default 20MB
    const MAX_TOTAL_SIZE_PER_HOUR = maxFileSizeMB * 1024 * 1024 * 3; // 3x the max file size to accommodate multiple uploads

    if (uploadCount >= MAX_UPLOADS_PER_HOUR) {
      return false; // Too many uploads
    }

    if (totalSize + fileSize > MAX_TOTAL_SIZE_PER_HOUR) {
      return false; // Total size would exceed limit
    }

    // Track this upload
    recentUploads.push({ timestamp: now, size: fileSize });
    this.userUploadTracking.set(userId, recentUploads);

    return true;
  }

  /**
   * Track AI usage and check for abuse
   */
  trackAiUsage(
    userId: string,
    estimatedCost: number = 0.01,
    tokens: number = 0,
  ): boolean {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    if (!this.aiUsageTracking.has(userId)) {
      this.aiUsageTracking.set(userId, []);
    }

    const userUsage = this.aiUsageTracking.get(userId)!;

    // Clean old entries (older than 1 hour)
    const recentUsage = userUsage.filter(
      (usage) => now - usage.timestamp < oneHour,
    );

    // Check limits for abuse detection
    const requestCount = recentUsage.length;
    const totalCost = recentUsage.reduce((sum, usage) => sum + usage.cost, 0);
    const totalTokens = recentUsage.reduce(
      (sum, usage) => sum + usage.tokens,
      0,
    );

    // Abuse thresholds per hour
    const MAX_AI_REQUESTS_PER_HOUR = 100;
    const MAX_ESTIMATED_COST_PER_HOUR = 5.0; // $5 per hour
    const MAX_TOKENS_PER_HOUR = 100000; // 100k tokens per hour

    // Log suspicious activity
    if (requestCount > MAX_AI_REQUESTS_PER_HOUR * 0.8) {
      // 80% threshold warning
      this.logger.warn(
        `HIGH_AI_USAGE: User ${userId} has made ${requestCount} AI requests in the last hour`,
      );
    }

    if (totalCost > MAX_ESTIMATED_COST_PER_HOUR * 0.8) {
      this.logger.warn(
        `HIGH_AI_COST: User ${userId} estimated cost $${totalCost.toFixed(2)} in the last hour`,
      );
    }

    // Block if thresholds exceeded
    if (requestCount >= MAX_AI_REQUESTS_PER_HOUR) {
      this.logger.error(
        `AI_ABUSE_DETECTED: User ${userId} exceeded request limit (${requestCount})`,
      );
      return false;
    }

    if (totalCost >= MAX_ESTIMATED_COST_PER_HOUR) {
      this.logger.error(
        `AI_COST_ABUSE: User ${userId} exceeded cost limit ($${totalCost.toFixed(2)})`,
      );
      return false;
    }

    // Track this usage
    recentUsage.push({ timestamp: now, cost: estimatedCost, tokens });
    this.aiUsageTracking.set(userId, recentUsage);

    return true;
  }

  /**
   * Get AI usage statistics
   */
  getAiUsageStats() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    let totalRequests = 0;
    let totalCost = 0;
    let totalTokens = 0;
    const activeUsers = new Set<string>();

    this.aiUsageTracking.forEach((usage, userId) => {
      const recentUsage = usage.filter((u) => now - u.timestamp < oneHour);
      totalRequests += recentUsage.length;
      totalCost += recentUsage.reduce((sum, u) => sum + u.cost, 0);
      totalTokens += recentUsage.reduce((sum, u) => sum + u.tokens, 0);
      if (recentUsage.length > 0) {
        activeUsers.add(userId);
      }
    });

    return {
      aiRequestsLastHour: totalRequests,
      estimatedCostLastHour: totalCost,
      totalTokensLastHour: totalTokens,
      activeAiUsers: activeUsers.size,
      averageCostPerRequest: totalRequests > 0 ? totalCost / totalRequests : 0,
    };
  }

  /**
   * Get upload statistics
   */
  getUploadStats() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    let totalUploads = 0;
    let totalSize = 0;
    const activeUsers = new Set<string>();

    this.userUploadTracking.forEach((uploads, userId) => {
      const recentUploads = uploads.filter(
        (upload) => now - upload.timestamp < oneHour,
      );
      totalUploads += recentUploads.length;
      totalSize += recentUploads.reduce((sum, upload) => sum + upload.size, 0);
      if (recentUploads.length > 0) {
        activeUsers.add(userId);
      }
    });

    return {
      uploadsLastHour: totalUploads,
      totalSizeLastHour: totalSize,
      activeUsers: activeUsers.size,
      averageFileSize:
        totalUploads > 0 ? Math.round(totalSize / totalUploads) : 0,
    };
  }
}

// ============ FILE VALIDATION SYSTEM ============

@Injectable()
export class FileValidationService {
  private readonly logger = new Logger(FileValidationService.name);

  /**
   * Enhanced file validation with magic number checking
   */
  validateFileContent(
    buffer: Buffer,
    mimetype: string,
    filename: string,
  ): boolean {
    const firstBytes = buffer.slice(0, 4);

    // Magic number validation for common file types
    const magicNumbers: { [key: string]: number[][] } = {
      'application/pdf': [[0x25, 0x50, 0x44, 0x46]], // %PDF
      'application/msword': [[0xd0, 0xcf, 0x11, 0xe0]], // MS Office
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        [
          [0x50, 0x4b, 0x03, 0x04], // ZIP-based formats
          [0x50, 0x4b, 0x05, 0x06],
          [0x50, 0x4b, 0x07, 0x08],
        ],
      'text/plain': [], // Text files don't have magic numbers, skip validation
      'image/jpeg': [[0xff, 0xd8, 0xff]],
      'image/png': [[0x89, 0x50, 0x4e, 0x47]],
    };

    const expectedMagicNumbers = magicNumbers[mimetype];

    // If no magic numbers defined for this type, allow it (for text files, etc.)
    if (!expectedMagicNumbers || expectedMagicNumbers.length === 0) {
      return true;
    }

    // Check if file starts with any of the expected magic numbers
    return expectedMagicNumbers.some((magic) => {
      return magic.every(
        (byte, index) =>
          index < firstBytes.length && firstBytes[index] === byte,
      );
    });
  }

  /**
   * Validate file type and extension
   */
  validateFileType(filename: string, contentType: string): boolean {
    const allowedTypes = [
      // PDF
      'application/pdf',
      // Microsoft Office
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      // Text files
      'text/plain',
      'text/csv',
      'text/html',
      'text/markdown',
      'application/rtf',
      'application/xml',
      'text/xml',
    ];

    const fileExtension = filename.toLowerCase().split('.').pop();
    const allowedExtensions = [
      'pdf',
      'doc',
      'docx',
      'ppt',
      'pptx',
      'xls',
      'xlsx',
      'txt',
      'csv',
      'html',
      'htm',
      'md',
      'rtf',
      'xml',
    ];

    // Check MIME type and file extension
    if (
      !allowedTypes.includes(contentType) ||
      !allowedExtensions.includes(fileExtension || '')
    ) {
      return false;
    }

    // Additional security: Check filename for path traversal attempts
    if (filename.includes('../') || filename.includes('..\\')) {
      return false;
    }

    return true;
  }
}
