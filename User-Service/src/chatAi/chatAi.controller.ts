import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
  Logger,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { ApiBearerAuth, ApiTags, ApiBody, ApiConsumes } from '@nestjs/swagger';
import { ChatAiService } from './chatAi.service';
import {
  CreateChatAiDto,
  UpdateChatAiDto,
  CreateDocumentDto,
  UpdateDocumentDto,
  CreateChatMessageDto,
  ChatQueryDto,
  FetchSingleChatAiDto,
  FetchChatAisDto,
  FetchSingleDocumentDto,
  FetchDocumentContentDto,
  FetchDocumentsDto,
  RemoveChatAiDto,
  UpdateChatAiSettingDto,
  RemoveDocumentDto,
} from './dto/chatAi.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { User } from '../user/entities/user.entity';

// Get max file size from environment variable
const getMaxFileSize = () =>
  parseInt(process.env.MAX_FILE_SIZE) || 20 * 1024 * 1024; // Default 20MB

@ApiTags('ChatAI')
@Controller('users/app/chatai')
export class ChatAiController {
  private readonly logger = new Logger(ChatAiController.name);

  constructor(private readonly chatAiService: ChatAiService) {}

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('setup-chatai')
  async setupChatAi(
    @Req() req: { user: User },
    @Body() createChatAiDto: CreateChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.setupChatAi(userId, createChatAiDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-chatai')
  async updateChatAi(
    @Req() req: { user: User },
    @Body() updateChatAiDto: UpdateChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateChatAi(userId, updateChatAiDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-settings')
  async updateChatAiSetting(
    @Req() req: { user: User },
    @Body() updateChatAiSettingDto: UpdateChatAiSettingDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateChatAiSettings(
      userId,
      updateChatAiSettingDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-single-chatai')
  async getSingleChatAi(
    @Req() req: { user: User },
    @Query() query: FetchSingleChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getSingleChatAi(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-all-chatais')
  async getAllChatAis(
    @Req() req: { user: User },
    @Query() query: FetchChatAisDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getAllChatAis(userId, query);
  }

  // @UseGuards(JwtAuthGuard)
  // @ApiBearerAuth()
  // @Get('get-transactions')
  // async getTransactions(
  //   @Query() query: GetChatAiTransactionDto,
  //   @Req() req: { user: User },
  // ) {
  //   const userId = req.user.id;
  //   return await this.chatAiService.getAllTransactions(userId, query);
  // }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('remove-chatai')
  async removeChatAi(
    @Req() req: { user: User },
    @Body() removeChatAiDto: RemoveChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.removeChatAi(userId, removeChatAiDto);
  }

  // ==================== Document Management ====================

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('upload-document')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        appId: {
          type: 'string',
          description: 'Application ID',
        },
        title: {
          type: 'string',
          description: 'Document title',
        },
        description: {
          type: 'string',
          description: 'Document description',
        },
      },
      required: ['file', 'appId'],
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        fileSize: getMaxFileSize(), // Configurable file size limit
      },
    }),
  )
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() createDocumentDto: CreateDocumentDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.uploadDocument(
      userId,
      file,
      createDocumentDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-documents')
  async getDocuments(
    @Query() query: FetchDocumentsDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getDocuments(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-document-content')
  async getDocumentContent(
    @Query() query: FetchDocumentContentDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getDocumentContent(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-document')
  async getSingleDocument(
    @Query() query: FetchSingleDocumentDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getSingleDocument(userId, query);
  }

  // ==================== Internal APIs (for ChatAI-SDK-Clean) ====================

  // Internal status update endpoint removed - ChatAI-SDK-Clean now updates database directly

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-document')
  async updateDocument(
    @Req() req: { user: User },
    @Body() updateDocumentDto: UpdateDocumentDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateDocument(userId, updateDocumentDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('remove-document')
  async removeDocument(
    @Req() req: { user: User },
    @Body() removeDocumentDto: RemoveDocumentDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.removeDocument(userId, removeDocumentDto);
  }

  // ==================== Chat Management ====================
  // Note: Chat functionality has been moved to ChatAI-SDK service
  // This service now only handles document management and app validation

  // ==================== Credit Management ====================
  // Note: Credit management has been moved to key-validator API optimization
}
